package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.service.dto.*;

import java.util.List;

import java.util.Map;

/**
* @description 待制工单Service
* <AUTHOR>
* @date 2025-07-30
**/
public interface AwaitingProductionOrderService {
    
    /**
	 * 分页查找查找 待制工单
	 * 
	 * @param dto 待制工单dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductionOrderVO>> findAwaitingProductionOrderPage(ProductionOrderDTO dto);

    /**
	 * 添加 \修改 待制工单
	 *
	 * @param awaitingProductionOrderDto 待制工单dto实体对象
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProductionOrderDTO awaitingProductionOrderDto);
	
	/**
	 * 通过id查找待制工单
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductionOrderVO> findAwaitingProductionOrder(String id);
	
	/**
	 * 删除 待制工单
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 待制工单
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductionOrderVO>> getOptionsList(String id);




    /**
     * 联动数据查询
     *
     * @param linkageDataTableName
     * @param conditionalValue
     * @return
     */
    TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue);

    Map<String,String> selectDataLinkageByOrderNumber(String order_number);
    Map<String,String> selectDataLinkageByProductModel(String product_model);
    /**
     * 根据条件查询待制工单列表
     *
     * @param dto 待制工单信息
     * @return 待制工单集合信息
     */
    List<ProductionOrderVO> selectAwaitingProductionOrderList(ProductionOrderDTO dto);

	/**
	 * 根据IDS获取待制工单数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectAwaitingProductionOrderListByIds(List<String> ids);

	/**
	 * 测评订单下发产品
	 * @param ids
	 * @return
	 */
    TorchResponse issuedProductByIds(List<String> ids);

	/**
	 * 指派负责人
	 * @param awaitingProductionOrderDto
	 * @return
	 */
	TorchResponse setResponsiblePerson(ProductionOrderDTO awaitingProductionOrderDto);

	/**
	 * 判断是否有同型号同批次已完工工单
	 * @param awaitingProductionOrderDto
	 * @return
	 */
    TorchResponse judgeModelAndBatch(ProductionOrderDTO awaitingProductionOrderDto);

	/**
	 * 保存同型同批二次检测标记
	 * @param id
	 * @return
	 */
	TorchResponse SaveWtstabr(ProductionOrderDTO id);

	/**
	 * 保存工单工序方案
	 *
	 * @param customerProcessSchemeDTO
	 * @param token
	 * @return
	 */
	TorchResponse saveCustomerProcessSheme(CustomerProcessSchemeDTO customerProcessSchemeDTO, String token);

	/**
	 * 表单审批
	 *
	 * @param formApprovalDTO
	 * @param token
	 * @return
	 */
	TorchResponse approve(FormApprovalDTO formApprovalDTO, String token);

	/**
	 * 审批审批 待制工单
	 *
	 * @param awaitingProductionOrderDto 用户管理dto实体对象
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse apply(ProductionOrderDTO awaitingProductionOrderDto,String token);

	/**
	 * 根据工序方案查询实验项目
	 * @param dxperimentProjectDTO
	 * @return
	 */
	TorchResponse getExperimentProjectByPlan(ExperimentProjectDTO dxperimentProjectDTO);
	/**
	 * 根据客户工序方案查询实验项目
	 * @param customerExperimentProjectDTO
	 * @return
	 */
	TorchResponse getExperimentProjectByCustomerPlan(CustomerExperimentProjectDTO customerExperimentProjectDTO);

	/**
	 * 查询工序
	 * @param standardProcessManagementDTO
	 * @return
	 */
	TorchResponse getStandProcessForCustomerPlan(StandardProcessManagementDTO standardProcessManagementDTO);

	/**
	 * 复制工单
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse copyProductionOrder(ProductionOrderDTO productionOrderDTO);

	/**
	 * 分单
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse splitProductionOrder(ProductionOrderDTO productionOrderDTO);

	/**
	 * 消除pda预警
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse cancelPdaWarning(ProductionOrderDTO productionOrderDTO);

	/**
	 * 暂停
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse pauseProductionOrder(ProductionOrderDTO productionOrderDTO);

	/**
	 * 取消订单
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse cancelProductionOrder(ProductionOrderDTO productionOrderDTO);

	/**
	 * 入库
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse productionOrderInstore(ProductionOrderDTO productionOrderDTO);

	/**
	 * 不可筛
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse connotScreened(ProductionOrderDTO productionOrderDTO);

	/**
	 * 外协反馈，
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse outSorucingBack(ProductionOrderDTO productionOrderDTO);

	/**
	 * 外协申请
	 * @param addOrUpdateOutsourcingDTO
	 * @return
	 */
	TorchResponse outSourcingApply(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO);

	/**
	 * 根据工序获取试验项目
	 * @param ids
	 * @return
	 */
	TorchResponse getExperimentProjectByProcess(String[] ids);

	/**
	 * 恢复
	 * @param productionOrderDTO
	 * @return
	 */
    TorchResponse restoreProductionOrder(ProductionOrderDTO productionOrderDTO);

	/**
	 * 下发任务
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse issueProductionOrderTask(ProductionOrderDTO productionOrderDTO);

	/**
	 * 查询工单绑定方案
	 * @param productionOrderDTO
	 * @return
	 */
	TorchResponse getBindProcessSchemePlan(ProductionOrderDTO productionOrderDTO);
}