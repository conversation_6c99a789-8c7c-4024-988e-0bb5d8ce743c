<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="增加工序相关几个字段增加工序相关几个字段">
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/mapper/QuartzJobMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/mapper/QuartzJobMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/quartz/InitQuartzJob.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/quartz/InitQuartzJob.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/constant/DicConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/constant/DicConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/constant/BusinessConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/constant/BusinessConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/ProductionTaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/ProductionTaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/domain/ProdTaskOpHist.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/domain/ProdTaskOpHist.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/domain/vo/ProdTaskOpHistVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/domain/vo/ProdTaskOpHistVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/ProductionTaskService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/ProductionTaskService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/dto/ProdTaskOpHistDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/dto/ProdTaskOpHistDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" afterDir="false" />
    </list>
    <list id="640866c8-be23-4972-8da4-c676321340e3" name="ignore-on-commit" comment="">
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/context/SecurityContextHolder.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/utils/SecurityUser.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\tools\mavenRep" />
        <option name="userSettingsFile" value="C:\tools\apache-maven-3.8.4-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zJXIm52poQbdHVZYIVkXliym0G" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.jx-mes [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "jdk.selected.JAVA_MODULE": "1.8",
    "last_opened_file_path": "C:/Work/C3S/compass/svn/JunXin/JunXin2025Code/trunk/02Engineering/04Implementation/jx-mes-code/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/mapping",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4",
    "service.view.auto.scroll.to.source": "true",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "应用程序.BasicApplication.executor": "Run",
    "应用程序.BusinessApplication.executor": "Debug",
    "应用程序.GatewayApplication.executor": "Run"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\src\main\java\com\huatek\frame\modules\system\service\mapping" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\impl" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\mapping" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-rpc\src\main\java\com\huatek\frame\modules\business\service\dto" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-rpc\src\main\java\com\huatek\frame\modules\business\service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.service.dto" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.BasicApplication">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BasicApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BasicApplication" />
      <module name="jx-mes-basic-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BusinessApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BusinessApplication" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.GatewayApplication" />
      <module name="jx-mes-gateway" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobAdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.admin.JobAdminApplication" />
      <module name="torch-job-admin" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobExecutorApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.executor.JobExecutorApplication" />
      <module name="torch-job-executor" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.BusinessApplication" />
        <item itemvalue="应用程序.BasicApplication" />
        <item itemvalue="应用程序.GatewayApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="" />
      <created>1751450073556</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751450073556</updated>
    </task>
    <task id="LOCAL-00001" summary="增加注释">
      <option name="closed" value="true" />
      <created>1754549532480</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754549532480</updated>
    </task>
    <task id="LOCAL-00002" summary="增加订单评审记录">
      <option name="closed" value="true" />
      <created>1754550273913</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754550273913</updated>
    </task>
    <task id="LOCAL-00003" summary="能力评审">
      <option name="closed" value="true" />
      <created>1754555935740</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754555935740</updated>
    </task>
    <task id="LOCAL-00004" summary="能力评审-异常反馈">
      <option name="closed" value="true" />
      <created>1754562565051</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754562565051</updated>
    </task>
    <task id="LOCAL-00005" summary="修改字符串类型">
      <option name="closed" value="true" />
      <created>1755078866749</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755078866749</updated>
    </task>
    <task id="LOCAL-00006" summary="分单">
      <option name="closed" value="true" />
      <created>1755079226062</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755079226062</updated>
    </task>
    <task id="LOCAL-00007" summary="外协申请外协审批通过，则状态变更为已外协，试验方式更新为外协&#10;外协验收,则状态变更为进行中">
      <option name="closed" value="true" />
      <created>1755080224564</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755080224564</updated>
    </task>
    <task id="LOCAL-00008" summary="修改权限关键字">
      <option name="closed" value="true" />
      <created>1755081488728</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755081488728</updated>
    </task>
    <task id="LOCAL-00009" summary="增加工序相关几个字段">
      <option name="closed" value="true" />
      <created>1755140814847</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755140814847</updated>
    </task>
    <option name="localTasksCounter" value="10" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="增加注释" />
    <MESSAGE value="增加订单评审记录" />
    <MESSAGE value="能力评审" />
    <MESSAGE value="能力评审-异常反馈" />
    <MESSAGE value="修改字符串类型" />
    <MESSAGE value="分单" />
    <MESSAGE value="外协申请外协审批通过，则状态变更为已外协，试验方式更新为外协&#10;外协验收,则状态变更为进行中" />
    <MESSAGE value="修改权限关键字" />
    <MESSAGE value="long-integer" />
    <MESSAGE value="增加工序相关几个字段" />
    <option name="LAST_COMMIT_MESSAGE" value="增加工序相关几个字段" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/utils/HttpClientUtil.java</url>
          <line>215</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/utils/HttpClientUtil.java</url>
          <line>223</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>