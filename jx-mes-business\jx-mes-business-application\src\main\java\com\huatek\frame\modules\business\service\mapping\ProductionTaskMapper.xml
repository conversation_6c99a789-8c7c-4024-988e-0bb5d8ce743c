<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductionTaskMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.task_number as taskNumber,
		t.work_order_number as workOrderNumber,
		t.execution_sequence as executionSequence,
		t.asso_wo_pred_proc as assoWoPredProc,
		t.related_work_order as relatedWorkOrder,
		t.inspection_quantity2 as inspectionQuantity2,
		t.process_name2 as processName2,
		t.test_basis as testBasis,
		t.test_conditions as testConditions,
		t.judgment_criteria as judgmentCriteria,
		t.status as status,
		t.technical_competency_number as technicalCompetencyNumber,
		t.operation_card as operationCard,
		t.`comment` as `comment`,
		t.ticket_level as ticketLevel,
		t.scheduled_start_time as scheduledStartTime,
		t.scheduled_end_time as scheduledEndTime,
		t.product_name as productName,
		t.product_model as productModel,
		t.manufacturer as manufacturer,
		t.batch_number as batchNumber,
		t.product_category as productCategory,
		t.product_information1 as productInformation1,
		t.entrusted_unit as entrustedUnit,
		t.`grouping` as `grouping`,
		t.test_methodology as testMethodology,
		t.actual_start_time as actualStartTime,
		t.actual_end_time as actualEndTime,
		t.test_type as testType,
		t.department as department,
		t.belonging_team2 as belongingTeam2,
		t.reporting_time0 as reportingTime0,
		t.pda_warning as pdaWarning,
		t.reporter4 as reporter4,
		t.non_conformity_number as nonConformityNumber,
		t.qualified_quantity as qualifiedQuantity,
		t.unqualified_quantity as unqualifiedQuantity,
		t.failure_mode as failureMode,
		t.completion_time6 as completionTime6,
		t.pda as pda,
		t.record_change_stamp as recordChangeStamp,
		t.humidity as humidity,
		t.temperature as temperature,
		t.test_result_summary as testResultSummary,
		t.report_work_remarks as reportWorkRemarks,
		t.attachment as attachment,
		t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
		t.pause_reason as pauseReason,
        t.workstation as workstation,
		t.completed_quantity as completedQuantity,
		t.customer_process_name as customerProcessName,
		t.display_number as displayNumber,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectProductionTaskPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductionTaskDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
		select
		<include refid="Base_Column_List" />
			from production_task t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="pauseReason != null and pauseReason != ''">
                    and t.pause_reason like concat('%', #{pauseReason} ,'%')
                </if>
                <if test="completedQuantity != null">
                    and t.completed_quantity = #{completedQuantity}
                </if>
                <if test="testBasis != null and testBasis != ''">
                    and t.test_basis  like concat('%', #{testBasis} ,'%')
                </if>
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  in (#{status})
                </if>
                <if test="technicalCompetencyNumber != null and technicalCompetencyNumber != ''">
                    and t.technical_competency_number  = #{technicalCompetencyNumber}
                </if>
                <if test="operationCard != null and operationCard != ''">
                    and t.operation_card  like concat('%', #{operationCard} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="ticketLevel != null and ticketLevel != ''">
                    and t.ticket_level  = #{ticketLevel}
                </if>
                <if test="scheduledStartTime != null">
                    and t.scheduled_start_time  = #{scheduledStartTime}
                </if>
                <if test="scheduledEndTime != null">
                    and t.scheduled_end_time  = #{scheduledEndTime}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping  like concat('%', #{grouping} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="actualStartTime != null">
                    and t.actual_start_time  = #{actualStartTime}
                </if>
                <if test="actualEndTime != null">
                    and t.actual_end_time  = #{actualEndTime}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="department != null and department != ''">
                    and t.department  = #{department}
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  = #{workstation}
                </if>
                <if test="belongingTeam2 != null and belongingTeam2 != ''">
                    and t.belonging_team2  like concat('%', #{belongingTeam2} ,'%')
                </if>
                <if test="reportingTime0 != null">
                    and t.reporting_time0  = #{reportingTime0}
                </if>
                <if test="pdaWarning != null and pdaWarning != ''">
                    and t.pda_warning  like concat('%', #{pdaWarning} ,'%')
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="nonConformityNumber != null and nonConformityNumber != ''">
                    and t.non_conformity_number  like concat('%', #{nonConformityNumber} ,'%')
                </if>
                <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                    and t.qualified_quantity  = #{qualifiedQuantity}
                </if>
                <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                    and t.unqualified_quantity  = #{unqualifiedQuantity}
                </if>
                <if test="failureMode != null and failureMode != ''">
                    and t.failure_mode  = #{failureMode}
                </if>
                <if test="completionTime6 != null">
                    and t.completion_time6  = #{completionTime6}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="recordChangeStamp != null and recordChangeStamp != ''">
                    and t.record_change_stamp  = #{recordChangeStamp}
                </if>
                <if test="humidity != null and humidity != ''">
                    and t.humidity  like concat('%', #{humidity} ,'%')
                </if>
                <if test="temperature != null and temperature != ''">
                    and t.temperature  like concat('%', #{temperature} ,'%')
                </if>
                <if test="testResultSummary != null and testResultSummary != ''">
                    and t.test_result_summary  like concat('%', #{testResultSummary} ,'%')
                </if>
                <if test="reportWorkRemarks != null and reportWorkRemarks != ''">
                    and t.report_work_remarks  like concat('%', #{reportWorkRemarks} ,'%')
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="displayNumber != null">
                    and t.display_number  = #{displayNumber}
                </if>

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByTechnicalCompetencyNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.capability_number label,
        	t.capability_number value
        from capability_asset t
        WHERE t.capability_number != ''
         and t.codex_torch_deleted = '0'
     </select>
     <select id="selectDataLinkageByTechnicalCompetencyNumber" parameterType="String"
             resultType="java.util.Map">
        select
            t.operation_card as operationCard
        from capability_asset t
        WHERE t.capability_number = #{capability_number}
            and t.codex_torch_deleted = '0'
     </select>

    <select id="selectProductionTaskList" parameterType="com.huatek.frame.modules.business.service.dto.ProductionTaskDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
		select
		<include refid="Base_Column_List" />
			from production_task t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="testBasis != null and testBasis != ''">
                    and t.test_basis  like concat('%', #{testBasis} ,'%')
                </if>
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="technicalCompetencyNumber != null and technicalCompetencyNumber != ''">
                    and t.technical_competency_number  = #{technicalCompetencyNumber}
                </if>
                <if test="operationCard != null and operationCard != ''">
                    and t.operation_card  like concat('%', #{operationCard} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="ticketLevel != null and ticketLevel != ''">
                    and t.ticket_level  = #{ticketLevel}
                </if>
                <if test="scheduledStartTime != null">
                    and t.scheduled_start_time  = #{scheduledStartTime}
                </if>
                <if test="scheduledEndTime != null">
                    and t.scheduled_end_time  = #{scheduledEndTime}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping  like concat('%', #{grouping} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="actualStartTime != null">
                    and t.actual_start_time  = #{actualStartTime}
                </if>
                <if test="actualEndTime != null">
                    and t.actual_end_time  = #{actualEndTime}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                 <if test="department != null and department != ''">
                    and t.department  = #{department}
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  = #{workstation}
                </if>
                <if test="belongingTeam2 != null and belongingTeam2 != ''">
                    and t.belonging_team2  like concat('%', #{belongingTeam2} ,'%')
                </if>
                <if test="reportingTime0 != null">
                    and t.reporting_time0  = #{reportingTime0}
                </if>
                <if test="pdaWarning != null and pdaWarning != ''">
                    and t.pda_warning  like concat('%', #{pdaWarning} ,'%')
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="nonConformityNumber != null and nonConformityNumber != ''">
                    and t.non_conformity_number  like concat('%', #{nonConformityNumber} ,'%')
                </if>
                <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                    and t.qualified_quantity  = #{qualifiedQuantity}
                </if>
                <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                    and t.unqualified_quantity  = #{unqualifiedQuantity}
                </if>
                <if test="failureMode != null and failureMode != ''">
                    and t.failure_mode  = #{failureMode}
                </if>
                <if test="completionTime6 != null">
                    and t.completion_time6  = #{completionTime6}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="recordChangeStamp != null and recordChangeStamp != ''">
                    and t.record_change_stamp  = #{recordChangeStamp}
                </if>
                <if test="humidity != null and humidity != ''">
                    and t.humidity  like concat('%', #{humidity} ,'%')
                </if>
                <if test="temperature != null and temperature != ''">
                    and t.temperature  like concat('%', #{temperature} ,'%')
                </if>
                <if test="testResultSummary != null and testResultSummary != ''">
                    and t.test_result_summary  like concat('%', #{testResultSummary} ,'%')
                </if>
                <if test="reportWorkRemarks != null and reportWorkRemarks != ''">
                    and t.report_work_remarks  like concat('%', #{reportWorkRemarks} ,'%')
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="displayNumber != null">
                    and t.display_number  = #{displayNumber}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProductionTaskListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
		select
		<include refid="Base_Column_List" />
			from production_task t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

  <select id="selectCurrentUserRoles" resultType="java.lang.String">
        select sr.role from sys_user_role sur left join sys_role sr on sur.role_id  = sr.id where sur.user_id =#{currentUserId}
    </select>

    <!-- 查询工单最后一个工序的合格数量 -->
    <select id="getLastProcessQualifiedQuantity" resultType="java.lang.Long">
        SELECT qualified_quantity
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND codex_torch_deleted = '0'
          AND execution_sequence = (
              SELECT MAX(execution_sequence)
              FROM production_task
              WHERE work_order_number = #{workOrderNumber}
                AND codex_torch_deleted = '0'
                AND execution_sequence IS NOT NULL
          )
        LIMIT 1
    </select>

    <!-- 查询工单的所有不合格工序信息 -->
    <select id="getWorkOrderQualityInfo" resultType="com.huatek.frame.modules.business.domain.vo.UnqualifiedProcessVO">
        SELECT
            process_name2 as processName,
            unqualified_quantity as unqualifiedQuantity,
            failure_mode as failureMode
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND codex_torch_deleted = '0'
          AND unqualified_quantity > 0
        ORDER BY execution_sequence ASC
    </select>

    <!-- 检查前一个工序是否已完成 -->
    <select id="checkPreviousProcessStatus" resultType="java.lang.String">
        SELECT status
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND execution_sequence = #{executionSequence} - 1
          AND codex_torch_deleted = '0'
        LIMIT 1
    </select>

    <!-- 检查关联工单前置工序是否已完成 -->
    <select id="checkRelatedProcessStatus" resultType="java.lang.String">
        SELECT status
        FROM production_task
        WHERE work_order_number = #{relatedWorkOrder}
          AND process_name2 = #{assoWoPredProc}
          AND codex_torch_deleted = '0'
        LIMIT 1
    </select>
</mapper>